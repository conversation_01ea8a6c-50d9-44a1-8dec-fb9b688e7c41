#!/usr/bin/env python3
"""
Setup script for EPG Data Ingestion
"""

import os
import sys
import subprocess
import shutil


def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        return False
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} detected")
    return True


def install_dependencies():
    """Install required dependencies"""
    print("Installing dependencies...")

    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False


def setup_environment():
    """Setup environment configuration"""
    print("Setting up environment configuration...")

    if not os.path.exists(".env"):
        if os.path.exists(".env.example"):
            shutil.copy(".env.example", ".env")
            print("✅ Created .env file from .env.example")
            print("⚠️  Please edit .env file with your Neo4j credentials")
        else:
            print("❌ .env.example file not found")
            return False
    else:
        print("✅ .env file already exists")

    return True


def create_directories():
    """Create necessary directories"""
    print("Creating directories...")

    directories = ["./logs"]

    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"✅ Created directory: {directory}")
        else:
            print(f"✅ Directory already exists: {directory}")

    return True


def run_basic_tests():
    """Run basic functionality tests"""
    print("Running basic tests...")

    try:
        result = subprocess.run([sys.executable, "test_basic.py"], capture_output=True, text=True)

        if result.returncode == 0:
            print("✅ Basic tests passed")
            return True
        else:
            print("❌ Basic tests failed:")
            print(result.stdout)
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ Failed to run tests: {e}")
        return False


def print_next_steps():
    """Print next steps for the user"""
    print("\n" + "="*50)
    print("🎉 Setup completed successfully!")
    print("="*50)
    print("\nNext steps:")
    print("1. Edit .env file with your Neo4j credentials:")
    print("   - NEO4J_URI (e.g., bolt://localhost:7687)")
    print("   - NEO4J_USERNAME (e.g., neo4j)")
    print("   - NEO4J_PASSWORD (your Neo4j password)")
    print()
    print("2. Start Neo4j database")
    print()
    print("3. Test connections:")
    print("   python cli.py test-all")
    print()
    print("4. Fetch sample data:")
    print("   python cli.py fetch --start-date 2025-01-20 --end-date 2025-01-21")
    print()
    print("5. Run full ingestion:")
    print("   python main.py")
    print()
    print("For more information, see README.md")


def main():
    """Main setup function"""
    print("EPG Data Ingestion Setup")
    print("=" * 30)

    steps = [
        ("Checking Python version", check_python_version),
        ("Installing dependencies", install_dependencies),
        ("Setting up environment", setup_environment),
        ("Creating directories", create_directories),
        ("Running basic tests", run_basic_tests)
    ]

    for step_name, step_func in steps:
        print(f"\n{step_name}...")
        if not step_func():
            print(f"❌ Setup failed at: {step_name}")
            return False

    print_next_steps()
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
