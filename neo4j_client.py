from neo4j import GraphDatabase
from typing import List, Dict, Any, Optional
import logging
from llm_extractor import LLMEntityExtractor


class Neo4jEPGClient:
    """Simplified Neo4j client for EPG data ingestion"""

    def __init__(self, uri: str, username: str, password: str, database: str = "neo4j",
                 enable_llm_extraction: bool = True, google_api_key: Optional[str] = None):
        self.driver = GraphDatabase.driver(uri, auth=(username, password))
        self.database = database
        self.logger = logging.getLogger(__name__)

        # Initialize LLM extractor
        self.llm_extractor = LLMEntityExtractor(api_key=google_api_key) if enable_llm_extraction else None
        if self.llm_extractor and self.llm_extractor.is_enabled():
            self.logger.info("LLM entity extraction enabled")
        else:
            self.logger.info("LLM entity extraction disabled")

    def close(self):
        """Close the database connection"""
        if self.driver:
            self.driver.close()

    def test_connection(self) -> bool:
        """Test database connection"""
        try:
            with self.driver.session(database=self.database) as session:
                result = session.run("RETURN 1 as test")
                return result.single()["test"] == 1
        except Exception as e:
            self.logger.error(f"Connection test failed: {e}")
            return False

    def create_constraints_and_indexes(self):
        """Create basic constraints and indexes"""
        constraints_and_indexes = [
            # Basic constraints for common node types
            "CREATE CONSTRAINT program_id_unique IF NOT EXISTS FOR (p:Program) REQUIRE p.id IS UNIQUE",
            "CREATE CONSTRAINT channel_id_unique IF NOT EXISTS FOR (c:Channel) REQUIRE c.id IS UNIQUE",

            # Basic indexes for performance
            "CREATE INDEX program_title_index IF NOT EXISTS FOR (p:Program) ON (p.title)",
            "CREATE INDEX program_start_time_index IF NOT EXISTS FOR (p:Program) ON (p.start_time)",
            "CREATE INDEX program_end_time_index IF NOT EXISTS FOR (p:Program) ON (p.end_time)",
            "CREATE INDEX channel_name_index IF NOT EXISTS FOR (c:Channel) ON (c.name)",
            "CREATE INDEX channel_number_index IF NOT EXISTS FOR (c:Channel) ON (c.channel_number)",
        ]

        with self.driver.session(database=self.database) as session:
            for query in constraints_and_indexes:
                try:
                    session.run(query)
                    self.logger.info(f"Executed: {query}")
                except Exception as e:
                    self.logger.warning(f"Failed to execute {query}: {e}")

    def clear_all_data(self):
        """Clear all data from the database"""
        query = "MATCH (n) DETACH DELETE n"
        with self.driver.session(database=self.database) as session:
            session.run(query)
            self.logger.info("Cleared all data from database")

    def ingest_epg_data(self, epg_data: List[Dict[str, Any]]) -> Dict[str, int]:
        """
        Simplified EPG data ingestion - directly inject response data as nodes

        Args:
            epg_data: Raw EPG response data (list of program dictionaries)

        Returns:
            Dictionary with counts of created nodes
        """
        total_programs = len(epg_data)
        self.logger.info(f"Starting simplified ingestion of {total_programs} programs")

        stats = {
            "nodes_created": 0,
            "programs_processed": 0,
            "channels_created": 0,
            "relationships_created": 0,
            "llm_entities_created": 0,
            "llm_relationships_created": 0,
            "llm_extractions_attempted": 0,
            "llm_extractions_successful": 0
        }

        # Progress tracking
        processed_count = 0
        last_logged_percentage = -1
        log_interval = max(1, total_programs // 20)  # Log every 5%

        with self.driver.session(database=self.database) as session:
            for program_data in epg_data:
                try:
                    # Create nodes and relationships from the program data
                    result = self._create_nodes_from_data(session, program_data, processed_count)
                    stats["nodes_created"] += result["nodes_created"]
                    stats["channels_created"] += result["channels_created"]
                    stats["relationships_created"] += result["relationships_created"]

                    # Perform LLM extraction after basic data insertion
                    if self.llm_extractor and self.llm_extractor.is_enabled():
                        llm_result = self._extract_and_create_llm_entities(
                            session, program_data, processed_count
                        )
                        stats["llm_entities_created"] += llm_result["entities_created"]
                        stats["llm_relationships_created"] += llm_result["relationships_created"]
                        stats["llm_extractions_attempted"] += 1
                        if llm_result["success"]:
                            stats["llm_extractions_successful"] += 1

                    stats["programs_processed"] += 1
                    processed_count += 1

                    # Log progress
                    percentage = (processed_count * 100) // total_programs
                    if (processed_count % log_interval == 0 or
                        percentage != last_logged_percentage and percentage % 5 == 0 or
                        processed_count == total_programs):

                        llm_info = ""
                        if self.llm_extractor and self.llm_extractor.is_enabled():
                            llm_info = (f", LLM Entities: {stats['llm_entities_created']}, "
                                      f"LLM Rels: {stats['llm_relationships_created']}")

                        self.logger.info(f"Progress: {processed_count}/{total_programs} programs processed ({percentage}%) - "
                                       f"Nodes: {stats['nodes_created']}, Channels: {stats['channels_created']}, "
                                       f"Relationships: {stats['relationships_created']}{llm_info}")
                        last_logged_percentage = percentage

                except Exception as e:
                    self.logger.error(f"Failed to process program: {e}")
                    processed_count += 1

        self.logger.info(f"✅ Ingestion completed! Processed {processed_count}/{total_programs} programs (100%)")
        self.logger.info(f"Final stats: {stats}")
        return stats

    def _create_nodes_from_data(self, session, program_data: Dict[str, Any], program_index: int) -> Dict[str, int]:
        """
        Create nodes and relationships from program data with proper channel handling

        Args:
            session: Neo4j session
            program_data: Raw program data dictionary
            program_index: Index for unique program identification

        Returns:
            Dictionary with counts of created nodes, channels, and relationships
        """
        result = {"nodes_created": 0, "channels_created": 0, "relationships_created": 0}

        # Create a unique program ID
        program_id = f"program_{program_index}_{program_data.get('title', 'unknown').replace(' ', '_')}"

        # Parse datetime strings for proper Neo4j datetime storage
        start_time = self._parse_datetime(program_data.get("start", ""))
        end_time = self._parse_datetime(program_data.get("end", ""))

        # Create main Program node with all program properties
        program_query = """
        CREATE (p:Program {
            id: $program_id,
            title: $title,
            name: $name,
            sport: $sport,
            rating: $rating,
            synopsis: $synopsis,
            start_time: $start_time,
            end_time: $end_time,
            is_live: $is_live,
            showmax: $showmax,
            thumbnail_uri: $thumbnail_uri,
            created_at: datetime()
        })
        """

        session.run(program_query, {
            "program_id": program_id,
            "title": program_data.get("title", ""),
            "name": program_data.get("name", ""),
            "sport": program_data.get("sport", ""),
            "rating": program_data.get("rating", ""),
            "synopsis": program_data.get("synopsis", ""),
            "start_time": start_time,
            "end_time": end_time,
            "is_live": program_data.get("isLive", False),
            "showmax": program_data.get("showmax", False),
            "thumbnail_uri": program_data.get("thumbnailUri", "")
        })
        result["nodes_created"] += 1

        # Handle channel data specially to create proper Channel nodes and relationships
        channels = program_data.get("channel", [])
        if isinstance(channels, list):
            for channel_data in channels:
                if isinstance(channel_data, dict):
                    # Create Channel node with all channel properties
                    channel_result = self._create_channel_node(session, channel_data)
                    result["channels_created"] += channel_result["channels_created"]
                    result["nodes_created"] += channel_result["nodes_created"]

                    # Create bidirectional relationships between Channel and Program
                    rel_result = self._create_channel_program_relationships(
                        session, channel_data.get("id", ""), program_id, start_time, end_time
                    )
                    result["relationships_created"] += rel_result

        # Create nodes for other key-value pairs (packages, subGenres, etc.)
        for key, value in program_data.items():
            if key == "channel":
                continue  # Already handled above
            elif key in ["packages", "subGenres"]:  # Handle list values
                if isinstance(value, list):
                    for item in value:
                        if item:  # Only create nodes for non-empty values
                            self._create_simple_node(session, key.capitalize().rstrip('s'), str(item))
                            result["nodes_created"] += 1
            elif value and not isinstance(value, (list, dict)):  # Simple key-value pairs
                self._create_simple_node(session, key.capitalize(), str(value))
                result["nodes_created"] += 1

        return result

    def _create_simple_node(self, session, label: str, value: str):
        """Create a simple node with label and value"""
        # Clean up label name
        clean_label = label.replace(" ", "").replace("-", "").replace("_", "")

        query = f"""
        MERGE (n:{clean_label} {{value: $value}})
        SET n.created_at = datetime()
        """

        session.run(query, {"value": value})

    def _parse_datetime(self, datetime_str: str) -> str:
        """Parse datetime string to Neo4j compatible format"""
        if not datetime_str:
            return ""

        try:
            # Parse the datetime string (format: "12/25/2024 14:00:00")
            from datetime import datetime
            dt = datetime.strptime(datetime_str, "%m/%d/%Y %H:%M:%S")
            # Return ISO format for Neo4j
            return dt.isoformat()
        except Exception as e:
            self.logger.warning(f"Failed to parse datetime '{datetime_str}': {e}")
            return datetime_str  # Return original if parsing fails

    def _create_channel_node(self, session, channel_data: Dict[str, Any]) -> Dict[str, int]:
        """Create Channel node with all channel properties"""
        result = {"channels_created": 0, "nodes_created": 0}

        channel_id = channel_data.get("id", "")
        if not channel_id:
            return result



        # Create Channel node with all properties
        channel_query = """
        MERGE (c:Channel {id: $channel_id})
        SET c.name = $name,
            c.channel_code = $channel_code,
            c.channel_number = $channel_number,
            c.stream = $stream,
            c.icon = $icon,
            c.mobile_icon = $mobile_icon,
            c.live_icon = $live_icon,
            c.square_icon = $square_icon,
            c.updated_at = datetime()
        """

        session.run(channel_query, {
            "channel_id": channel_id,
            "name": channel_data.get("name", ""),
            "channel_code": channel_data.get("channelCode", ""),
            "channel_number": channel_data.get("channelNumber", 0),
            "stream": channel_data.get("stream", ""),
            "icon": channel_data.get("icon", ""),
            "mobile_icon": channel_data.get("mobileIcon", ""),
            "live_icon": channel_data.get("liveIcon", ""),
            "square_icon": channel_data.get("squareIcon", "")
        })

        result["channels_created"] = 1
        result["nodes_created"] = 1
        return result

    def _create_channel_program_relationships(self, session, channel_id: str, program_id: str,
                                            start_time: str, end_time: str) -> int:
        """Create bidirectional relationships between Channel and Program with datetime"""
        if not channel_id or not program_id:
            return 0

        relationships_created = 0

        # Create BROADCASTS relationship (Channel -> Program)
        broadcasts_query = """
        MATCH (c:Channel {id: $channel_id})
        MATCH (p:Program {id: $program_id})
        MERGE (c)-[r:BROADCASTS]->(p)
        SET r.start_time = $start_time,
            r.end_time = $end_time,
            r.created_at = datetime()
        """

        session.run(broadcasts_query, {
            "channel_id": channel_id,
            "program_id": program_id,
            "start_time": start_time,
            "end_time": end_time
        })
        relationships_created += 1

        # Create AIRED_ON relationship (Program -> Channel) - bidirectional
        aired_on_query = """
        MATCH (p:Program {id: $program_id})
        MATCH (c:Channel {id: $channel_id})
        MERGE (p)-[r:AIRED_ON]->(c)
        SET r.start_time = $start_time,
            r.end_time = $end_time,
            r.created_at = datetime()
        """

        session.run(aired_on_query, {
            "program_id": program_id,
            "channel_id": channel_id,
            "start_time": start_time,
            "end_time": end_time
        })
        relationships_created += 1

        return relationships_created

    def _extract_and_create_llm_entities(self, session, program_data: Dict[str, Any],
                                        program_index: int) -> Dict[str, Any]:
        """Extract entities using LLM and create them in Neo4j"""
        result = {"entities_created": 0, "relationships_created": 0, "success": False}

        try:
            title = program_data.get("title", "")
            synopsis = program_data.get("synopsis", "")

            if not title and not synopsis:
                return result

            # Extract entities using LLM
            extraction_result = self.llm_extractor.extract_entities_from_program(title, synopsis)

            if extraction_result.get("error"):
                self.logger.warning(f"LLM extraction failed: {extraction_result['error']}")
                return result

            entities = extraction_result.get("entities", [])
            relationships = extraction_result.get("relationships", [])

            # Create LLM-extracted entities
            for entity in entities:
                self._create_llm_entity(session, entity)
                result["entities_created"] += 1

            # Create LLM-extracted relationships
            for relationship in relationships:
                if self._create_llm_relationship(session, relationship):
                    result["relationships_created"] += 1

            result["success"] = True
            self.logger.debug(f"LLM extraction successful: {len(entities)} entities, {len(relationships)} relationships")

        except Exception as e:
            self.logger.error(f"LLM extraction failed for program {program_index}: {e}")

        return result

    def _create_llm_entity(self, session, entity: Dict[str, Any]):
        """Create an LLM-extracted entity in Neo4j"""
        label = entity.get("label", "Entity")
        name = entity.get("name", "")
        properties = entity.get("properties", {})

        if not name:
            return

        # Clean label name for Neo4j
        clean_label = label.replace(" ", "").replace("-", "").replace("_", "")

        # Create entity with LLM source tag
        query = f"""
        MERGE (e:{clean_label} {{name: $name}})
        SET e.source = 'llm_extracted',
            e.created_at = datetime()
        """

        # Add any additional properties
        for key, value in properties.items():
            if key not in ['name', 'source', 'created_at']:
                query += f", e.{key} = ${key}"

        params = {"name": name}
        params.update(properties)

        session.run(query, params)

    def _create_llm_relationship(self, session, relationship: Dict[str, Any]) -> bool:
        """Create an LLM-extracted relationship in Neo4j"""
        try:
            source = relationship.get("source", "")
            target = relationship.get("target", "")
            rel_type = relationship.get("type", "RELATED_TO")
            properties = relationship.get("properties", {})

            if not source or not target:
                return False

            # Create relationship between entities (find by name across all labels)
            query = """
            MATCH (s) WHERE s.name = $source
            MATCH (t) WHERE t.name = $target
            MERGE (s)-[r:""" + rel_type + """]->(t)
            SET r.source = 'llm_extracted',
                r.created_at = datetime()
            """

            # Add any additional properties
            for key, value in properties.items():
                if key not in ['source', 'created_at']:
                    query += f", r.{key} = ${key}"

            params = {"source": source, "target": target}
            params.update(properties)

            session.run(query, params)
            return True

        except Exception as e:
            self.logger.warning(f"Failed to create LLM relationship: {e}")
            return False


    def get_statistics(self) -> Dict[str, Any]:
        """Get enhanced database statistics including channels and relationships"""
        queries = {
            "total_nodes": "MATCH (n) RETURN count(n) as count",
            "total_programs": "MATCH (p:Program) RETURN count(p) as count",
            "total_channels": "MATCH (c:Channel) RETURN count(c) as count",
            "total_relationships": "MATCH ()-[r]->() RETURN count(r) as count",
            "broadcasts_relationships": "MATCH ()-[r:BROADCASTS]->() RETURN count(r) as count",
            "aired_on_relationships": "MATCH ()-[r:AIRED_ON]->() RETURN count(r) as count",
            "llm_extracted_nodes": "MATCH (n {source: 'llm_extracted'}) RETURN count(n) as count",
            "llm_extracted_relationships": "MATCH ()-[r {source: 'llm_extracted'}]->() RETURN count(r) as count",
            "abbreviation_relationships": "MATCH ()-[r:ABBREVIATION_OF]->() RETURN count(r) as count",
            "node_labels": """
                CALL db.labels() YIELD label
                RETURN collect(label) as labels
            """,
            "sample_nodes": """
                MATCH (n)
                RETURN labels(n)[0] as label, count(n) as count
                ORDER BY count DESC
                LIMIT 10
            """,
            "channel_program_counts": """
                MATCH (c:Channel)-[:BROADCASTS]->(p:Program)
                RETURN c.name as channel_name, c.channel_number as channel_number, count(p) as program_count
                ORDER BY program_count DESC
                LIMIT 10
            """,
            "llm_entity_types": """
                MATCH (n {source: 'llm_extracted'})
                RETURN labels(n)[0] as entity_type, count(n) as count
                ORDER BY count DESC
                LIMIT 10
            """,
            "abbreviation_mappings": """
                MATCH (abbr)-[r:ABBREVIATION_OF]->(full)
                RETURN abbr.name as abbreviation, full.name as full_name, labels(abbr)[0] as type
                ORDER BY type, abbreviation
                LIMIT 20
            """
        }

        stats = {}
        with self.driver.session(database=self.database) as session:
            for key, query in queries.items():
                try:
                    result = session.run(query)
                    list_queries = ["node_labels", "sample_nodes", "channel_program_counts",
                                  "llm_entity_types", "abbreviation_mappings"]
                    if key in list_queries:
                        stats[key] = [dict(record) for record in result]
                    else:
                        stats[key] = result.single()["count"]
                except Exception as e:
                    self.logger.error(f"Failed to get {key}: {e}")
                    stats[key] = 0 if key not in ["node_labels", "sample_nodes", "channel_program_counts"] else []

        return stats

    def search_programs(self, search_term: str, limit: int = 20) -> List[Dict[str, Any]]:
        """Search programs by title or synopsis"""
        query = """
        MATCH (p:Program)
        WHERE toLower(p.title) CONTAINS toLower($search_term)
           OR toLower(p.synopsis) CONTAINS toLower($search_term)
        RETURN p.title as title, p.synopsis as synopsis,
               p.start_time as start_time, p.sport as sport
        ORDER BY p.start_time DESC
        LIMIT $limit
        """

        with self.driver.session(database=self.database) as session:
            result = session.run(query, {"search_term": search_term, "limit": limit})
            return [dict(record) for record in result]

    def get_channel_programs(self, channel_id: str = None, limit: int = 10) -> List[Dict[str, Any]]:
        """Get programs for a specific channel or all channel-program relationships"""
        if channel_id:
            query = """
            MATCH (c:Channel {id: $channel_id})-[r:BROADCASTS]->(p:Program)
            RETURN c.name as channel_name, c.channel_number as channel_number,
                   p.title as program_title, p.sport as sport,
                   r.start_time as start_time, r.end_time as end_time
            ORDER BY r.start_time
            LIMIT $limit
            """
            params = {"channel_id": channel_id, "limit": limit}
        else:
            query = """
            MATCH (c:Channel)-[r:BROADCASTS]->(p:Program)
            RETURN c.name as channel_name, c.channel_number as channel_number,
                   p.title as program_title, p.sport as sport,
                   r.start_time as start_time, r.end_time as end_time
            ORDER BY c.channel_number, r.start_time
            LIMIT $limit
            """
            params = {"limit": limit}

        with self.driver.session(database=self.database) as session:
            result = session.run(query, params)
            return [dict(record) for record in result]