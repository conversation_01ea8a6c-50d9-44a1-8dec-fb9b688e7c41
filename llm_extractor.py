"""
LLM-based entity extraction for EPG data using Gemini 2.0 Flash
"""

import logging
import os
from typing import List, Dict, Any, Optional
from dataclasses import dataclass

try:
    from langchain_google_genai import ChatGoogleGenerativeAI
    from langchain_experimental.graph_transformers import LLMGraphTransformer
    from langchain_core.documents import Document
    from langchain_core.prompts import Chat<PERSON>romptTemplate
    LANGCHAIN_AVAILABLE = True
except ImportError:
    LANGCHAIN_AVAILABLE = False


@dataclass
class ExtractedEntity:
    """Represents an extracted entity with its properties"""
    label: str
    name: str
    properties: Dict[str, Any] = None


@dataclass
class ExtractedRelationship:
    """Represents an extracted relationship between entities"""
    source: str
    target: str
    relationship_type: str
    properties: Dict[str, Any] = None


class LLMEntityExtractor:
    """LLM-based entity extractor for EPG sports data"""
    
    def __init__(self, api_key: Optional[str] = None):
        self.logger = logging.getLogger(__name__)
        
        if not LANGCHAIN_AVAILABLE:
            self.logger.warning("LangChain packages not available. LLM extraction will be disabled.")
            self.enabled = False
            return
            
        self.api_key = api_key or os.getenv("GOOGLE_API_KEY")
        if not self.api_key:
            self.logger.warning("Google API key not found. LLM extraction will be disabled.")
            self.enabled = False
            return
            
        self.enabled = True
        self.llm = None
        self.transformer = None
        
        # Define allowed nodes and relationships for sports data
        self.allowed_nodes = [
            "Tournament", "Team", "Venue", "Location", "Competition", 
            "Format", "Content", "Country", "Player", "League", "Series"
        ]
        
        self.allowed_relationships = [
            "ABBREVIATION_OF", "COMPETES_IN", "TAKES_PLACE_AT", "LOCATED_IN",
            "PLAYS_FOR", "PART_OF", "HOSTED_BY", "FEATURES", "INVOLVES"
        ]
        
    def initialize_llm(self):
        """Initialize the LLM and transformer"""
        if not self.enabled:
            return False
            
        try:
            # Initialize Gemini 2.0 Flash
            self.llm = ChatGoogleGenerativeAI(
                model="gemini-2.0-flash-exp",
                google_api_key=self.api_key,
                temperature=0.1,  # Low temperature for consistent extraction
                max_tokens=2048
            )
            
            # Create transformer with custom prompt
            self.transformer = self.create_transformer()
            self.logger.info("LLM entity extractor initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize LLM: {e}")
            self.enabled = False
            return False
    
    def create_custom_prompt(self) -> str:
        """Create custom prompt for EPG sports data extraction"""
        return """
You are an expert sports data analyst. Extract entities and relationships from EPG (Electronic Program Guide) sports content.

IMPORTANT RULES:
1. Extract BOTH abbreviations AND full names as separate entities
2. Create ABBREVIATION_OF relationships between abbreviations and full names
3. Expand all sports abbreviations using context from synopsis
4. Focus on sports entities: tournaments, teams, venues, locations, formats, content types
5. Use full country/team names, not abbreviations in entity names
6. Create comprehensive relationship networks

ENTITY TYPES TO EXTRACT:
- Tournament: Competition names (both abbreviated and full)
- Team: Team/country names (both abbreviated and full) 
- Venue: Stadium/ground names
- Location: Cities, countries, regions
- Competition: Specific competition instances
- Format: Match formats (Test, ODI, T20, etc.)
- Content: Content types (Highlights, Live, etc.)

RELATIONSHIP TYPES TO CREATE:
- ABBREVIATION_OF: Links abbreviations to full names
- COMPETES_IN: Teams/countries participating in tournaments
- TAKES_PLACE_AT: Events at venues
- LOCATED_IN: Venues in locations
- PART_OF: Sub-competitions of larger tournaments

EXAMPLE:
Title: "WTC HL '25 Final: RSA v AUS Test Wrap"
Synopsis: "ICC World Test Championship Highlights - Final: South Africa vs Australia Test Wrap. From Lord's Cricket Ground - London, England."

Extract:
- Tournament: "ICC World Test Championship", "WTC"
- Team: "South Africa", "RSA", "Australia", "AUS"  
- Venue: "Lord's Cricket Ground"
- Location: "London, England"
- Format: "Test Match"
- Content: "Highlights", "HL"

Relationships:
- "WTC" -[ABBREVIATION_OF]-> "ICC World Test Championship"
- "RSA" -[ABBREVIATION_OF]-> "South Africa"
- "AUS" -[ABBREVIATION_OF]-> "Australia"
- "HL" -[ABBREVIATION_OF]-> "Highlights"

Focus on creating a rich knowledge graph that captures the complete sports context.
"""

    def create_transformer(self) -> 'LLMGraphTransformer':
        """Create and configure the LLM Graph Transformer with custom EPG prompt"""

        # Create a ChatPromptTemplate from the custom prompt
        custom_prompt_text = self.create_custom_prompt()

        # Create a ChatPromptTemplate with the custom prompt
        prompt_template = ChatPromptTemplate.from_messages([
            ("system", custom_prompt_text),
            ("human", "Extract entities and relationships from the following text:\n\n{input}")
        ])

        additional_instructions = """
Focus on extracting sports entities with full names. Expand all abbreviations using context.
Create relationships that show the complete sports hierarchy and connections.
Ensure all country and team names are in full form, not abbreviated.
Extract both abbreviated and full forms as separate entities with ABBREVIATION_OF relationships.
"""

        transformer = LLMGraphTransformer(
            llm=self.llm,
            allowed_nodes=self.allowed_nodes,
            allowed_relationships=self.allowed_relationships,
            prompt=prompt_template,
            node_properties=False,  # Disable automatic property extraction for consistency
            relationship_properties=False,  # Disable automatic relationship properties
            additional_instructions=additional_instructions
        )
        return transformer
    
    def extract_entities_from_program(self, title: str, synopsis: str) -> Dict[str, Any]:
        """Extract entities and relationships from program title and synopsis"""
        if not self.enabled:
            return {"entities": [], "relationships": [], "error": "LLM extraction disabled"}
            
        if not self.transformer:
            if not self.initialize_llm():
                return {"entities": [], "relationships": [], "error": "Failed to initialize LLM"}
        
        try:
            # Combine title and synopsis for context
            combined_text = f"Title: {title}\nSynopsis: {synopsis}"
            
            # Create document
            document = Document(page_content=combined_text)
            
            # Convert to graph documents
            graph_documents = self.transformer.convert_to_graph_documents([document])
            
            if not graph_documents:
                return {"entities": [], "relationships": [], "error": "No entities extracted"}
            
            graph_doc = graph_documents[0]
            
            # Extract entities
            entities = []
            for node in graph_doc.nodes:
                entities.append({
                    "label": node.type,
                    "name": node.id,
                    "properties": node.properties or {}
                })
            
            # Extract relationships
            relationships = []
            for rel in graph_doc.relationships:
                relationships.append({
                    "source": rel.source.id,
                    "target": rel.target.id,
                    "type": rel.type,
                    "properties": rel.properties or {}
                })
            
            self.logger.debug(f"Extracted {len(entities)} entities and {len(relationships)} relationships")
            
            return {
                "entities": entities,
                "relationships": relationships,
                "error": None
            }
            
        except Exception as e:
            self.logger.error(f"Failed to extract entities: {e}")
            return {"entities": [], "relationships": [], "error": str(e)}
    
    def is_enabled(self) -> bool:
        """Check if LLM extraction is enabled and available"""
        return self.enabled and LANGCHAIN_AVAILABLE
