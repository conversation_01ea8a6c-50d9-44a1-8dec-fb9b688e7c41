import os
from typing import Optional
from pydantic import BaseModel, Field
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()


class Neo4jConfig(BaseModel):
    """Neo4j database configuration"""
    uri: str = Field(default="bolt://localhost:7687", description="Neo4j URI")
    username: str = <PERSON>(default="neo4j", description="Neo4j username")
    password: str = Field(..., description="Neo4j password")
    database: str = Field(default="neo4j", description="Neo4j database name")

    @classmethod
    def from_env(cls) -> "Neo4jConfig":
        """Create config from environment variables"""
        return cls(
            uri=os.getenv("NEO4J_URI", "bolt://localhost:7687"),
            username=os.getenv("NEO4J_USERNAME", "neo4j"),
            password=os.getenv("NEO4J_PASSWORD", ""),
            database=os.getenv("NEO4J_DATABASE", "neo4j")
        )


class APIConfig(BaseModel):
    """API configuration"""
    base_url: str = Field(
        default="https://supersport.com/apix/guide/v5.3",
        description="SuperSport API base URL"
    )
    timeout: int = Field(default=30, description="Request timeout in seconds")
    retry_count: int = Field(default=3, description="Number of retry attempts")
    retry_delay: float = Field(default=1.0, description="Initial retry delay in seconds")

    # EPG Request defaults
    default_country_code: str = Field(default="za", description="Default country code for EPG requests")
    default_channel_only: bool = Field(default=False, description="Default channel only flag for EPG requests")
    default_live_only: bool = Field(default=False, description="Default live only flag for EPG requests")
    default_start_date: str = Field(default="2025-05-27", description="Default start date for EPG requests (YYYY-MM-DD)")
    default_end_date: str = Field(default="2025-05-28", description="Default end date for EPG requests (YYYY-MM-DD)")

    @classmethod
    def from_env(cls) -> "APIConfig":
        """Create config from environment variables"""
        return cls(
            base_url=os.getenv("SUPERSPORT_API_URL", "https://supersport.com/apix/guide/v5.3"),
            timeout=int(os.getenv("API_TIMEOUT", "30")),
            retry_count=int(os.getenv("API_RETRY_COUNT", "3")),
            retry_delay=float(os.getenv("API_RETRY_DELAY", "1.0")),
            default_country_code=os.getenv("DEFAULT_COUNTRY_CODE", "za"),
            default_channel_only=os.getenv("DEFAULT_CHANNEL_ONLY", "false").lower() == "true",
            default_live_only=os.getenv("DEFAULT_LIVE_ONLY", "false").lower() == "true",
            default_start_date=os.getenv("DEFAULT_START_DATE", "2025-05-27"),
            default_end_date=os.getenv("DEFAULT_END_DATE", "2025-05-28")
        )


class AppConfig(BaseModel):
    """Application configuration"""
    log_level: str = Field(default="INFO", description="Logging level")
    log_format: str = Field(
        default="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        description="Log format"
    )
    days_per_request: Optional[int] = Field(
        default=None,
        description="Deprecated: Previously used to split date ranges into smaller requests"
    )

    @classmethod
    def from_env(cls) -> "AppConfig":
        """Create config from environment variables"""
        # Handle deprecated DAYS_PER_REQUEST parameter
        days_per_request_env = os.getenv("DAYS_PER_REQUEST")
        days_per_request = int(days_per_request_env) if days_per_request_env else None

        return cls(
            log_level=os.getenv("LOG_LEVEL", "INFO"),
            log_format=os.getenv("LOG_FORMAT", "%(asctime)s - %(name)s - %(levelname)s - %(message)s"),
            days_per_request=days_per_request
        )


class Config(BaseModel):
    """Main application configuration"""
    neo4j: Neo4jConfig
    api: APIConfig
    app: AppConfig

    @classmethod
    def load(cls) -> "Config":
        """Load configuration from environment variables"""
        return cls(
            neo4j=Neo4jConfig.from_env(),
            api=APIConfig.from_env(),
            app=AppConfig.from_env()
        )

    def validate_config(self) -> bool:
        """Validate configuration"""
        errors = []

        # Check Neo4j password
        if not self.neo4j.password:
            errors.append("Neo4j password is required")

        if errors:
            raise ValueError(f"Configuration errors: {', '.join(errors)}")

        return True


# Global config instance
config = Config.load()
