import logging
import sys
import json
import os

from config import config
from api_client import SuperSportAPIClient, create_epg_request
from neo4j_client import Neo4jEPGClient


def setup_logging():
    """Setup logging configuration"""
    logging.basicConfig(
        level=getattr(logging, config.app.log_level.upper()),
        format=config.app.log_format,
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('epg_ingestion.log')
        ]
    )


def main():
    """Main application entry point"""
    setup_logging()
    logger = logging.getLogger(__name__)

    logger.info("Starting EPG Data Ingestion")

    try:
        # Validate configuration
        config.validate_config()
        logger.info("Configuration validated successfully")

        # Initialize clients
        api_client = SuperSportAPIClient(base_url=config.api.base_url)

        # Get Google API key for LLM extraction (optional)
        google_api_key = os.getenv("GOOGLE_API_KEY")

        neo4j_client = Neo4jEPGClient(
            uri=config.neo4j.uri,
            username=config.neo4j.username,
            password=config.neo4j.password,
            database=config.neo4j.database,
            enable_llm_extraction=True,  # Enable LLM extraction
            google_api_key=google_api_key
        )

        # Test connections
        logger.info("Testing API connection...")
        if not api_client.test_connection():
            logger.error("Failed to connect to SuperSport API")
            return False
        logger.info("API connection successful")

        logger.info("Testing Neo4j connection...")
        if not neo4j_client.test_connection():
            logger.error("Failed to connect to Neo4j database")
            return False
        logger.info("Neo4j connection successful")

        # Setup database schema
        logger.info("Setting up database constraints and indexes...")
        neo4j_client.create_constraints_and_indexes()

        # Create EPG request using all config defaults (including dates)
        request = create_epg_request()

        logger.info(f"Fetching EPG data from {request.start_date_time} to {request.end_date_time}")

        # Fetch data from API for the entire date range
        epg_response = api_client.fetch_epg_data(
            request,
            retry_count=config.api.retry_count,
            delay=config.api.retry_delay
        )

        if not epg_response:
            logger.error("Failed to fetch EPG data")
            return False

        logger.info(f"Fetched {len(epg_response.programs)} programs")

        # Convert EPG response to raw data for simplified ingestion
        # Use by_alias=True to preserve original API field names (camelCase)
        raw_epg_data = [program.model_dump(by_alias=True) for program in epg_response.programs]

        # Ingest into Neo4j
        logger.info("Ingesting data into Neo4j...")
        stats = neo4j_client.ingest_epg_data(raw_epg_data)

        logger.info(f"Ingestion completed. Stats: {stats}")

        # Final statistics
        logger.info(f"Total programs processed: {len(raw_epg_data)}")
        logger.info(f"Ingestion stats: {stats}")

        # Get database statistics
        db_stats = neo4j_client.get_statistics()
        logger.info(f"Final database statistics: {json.dumps(db_stats, indent=2, default=str)}")

        logger.info("EPG Data Ingestion completed successfully")
        return True

    except Exception as e:
        logger.error(f"EPG Data Ingestion failed: {e}", exc_info=True)
        return False

    finally:
        # Cleanup
        if 'neo4j_client' in locals():
            neo4j_client.close()


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
