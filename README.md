# EPG Data Ingestion

A comprehensive Python application for fetching Electronic Program Guide (EPG) data from SuperSport API and storing it in a Neo4j knowledge graph database.

## Features

- **API Integration**: Fetches EPG data from SuperSport API with robust error handling and retry logic
- **Data Modeling**: Pydantic models for request/response validation and data structure
- **Neo4j Knowledge Graph**: Stores EPG data as interconnected nodes and relationships
- **Flexible Date Ranges**: Handles any date range in a single API request
- **Configuration Management**: Environment-based configuration with validation
- **CLI Tools**: Command-line utilities for testing, data fetching, and database management
- **Comprehensive Logging**: Detailed logging with configurable levels

## Architecture

### Data Model

The application creates the following Neo4j nodes and relationships:

**Nodes:**
- `Channel`: TV channels with metadata (name, number, icons, etc.)
- `Program`: TV programs/events with details (title, synopsis, timing, etc.)
- `Sport`: Sport categories
- `Genre`: Program genres/sub-genres
- `Package`: Subscription packages (e.g., DSTV_NOW)

**Relationships:**
- `Channel -[:BROADCASTS]-> Program`: Channel broadcasts program at specific time
- `Program -[:BELONGS_TO]-> Sport`: Program belongs to sport category
- `Program -[:HAS_GENRE]-> Genre`: Program has specific genre
- `Program -[:AVAILABLE_ON]-> Package`: Program available on package

### Project Structure

```
├── main.py              # Main application entry point
├── cli.py               # Command-line interface utilities
├── config.py            # Configuration management
├── models.py            # Pydantic data models
├── api_client.py        # SuperSport API client
├── neo4j_client.py      # Neo4j database client
├── requirements.txt     # Python dependencies
├── .env.example         # Environment configuration template
└── README.md           # This file
```

## Installation

1. **Clone the repository:**
   ```bash
   git clone <repository-url>
   cd EPG_DATA_INGESTION
   ```

2. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Setup environment configuration:**
   ```bash
   cp .env.example .env
   # Edit .env with your Neo4j credentials
   ```

4. **Setup Neo4j database:**
   - Install Neo4j Desktop or use Neo4j Cloud
   - Create a new database
   - Update `.env` with connection details

## Configuration

Create a `.env` file with the following variables:

```env
# Neo4j Database Configuration
NEO4J_URI=bolt://localhost:7687
NEO4J_USERNAME=neo4j
NEO4J_PASSWORD=your_password_here
NEO4J_DATABASE=neo4j

# SuperSport API Configuration
SUPERSPORT_API_URL=https://supersport.com/apix/guide/v5.3
API_TIMEOUT=30
API_RETRY_COUNT=3
API_RETRY_DELAY=1.0

# EPG Request Defaults
DEFAULT_COUNTRY_CODE=za
DEFAULT_CHANNEL_ONLY=false
DEFAULT_LIVE_ONLY=false
DEFAULT_START_DATE=2025-05-27
DEFAULT_END_DATE=2025-05-28

# Application Configuration
LOG_LEVEL=INFO
```

## Usage

### Main Application

Run the main ingestion process:

```bash
python main.py
```

This will:
1. Fetch EPG data for the configured date range (default: 2025-05-27 to 2025-05-28) in a single API request
2. Store data in Neo4j knowledge graph
3. Create comprehensive relationships between entities
4. Generate statistics and logs

### CLI Utilities

The CLI provides various utilities for testing and management:

**Test connections:**
```bash
python cli.py test-all          # Test both API and Neo4j
python cli.py test-api          # Test SuperSport API only
python cli.py test-neo4j        # Test Neo4j connection only
```

**Fetch sample data:**
```bash
python cli.py fetch --start-date 2025-01-20 --end-date 2025-01-21
python cli.py fetch --start-date 2025-01-20 --end-date 2025-01-21 --output sample.json
```

**Database operations:**
```bash
python cli.py stats             # Get database statistics
python cli.py search "football" # Search programs
python cli.py search "hockey" --limit 5
python cli.py clear             # Clear all data (with confirmation)
```

## API Details

The application fetches data from:
```
https://supersport.com/apix/guide/v5.3/tvguide?countryCode=za&channelOnly=false&startDateTime=2025-05-27&endDateTime=2025-05-28&liveOnly=false
```

### Request Parameters:
- `countryCode`: Country code (configurable via DEFAULT_COUNTRY_CODE, default: "za")
- `channelOnly`: Whether to fetch channels only (configurable via DEFAULT_CHANNEL_ONLY, default: false)
- `startDateTime`: Start date in YYYY-MM-DD format (configurable via DEFAULT_START_DATE, default: "2025-05-27")
- `endDateTime`: End date in YYYY-MM-DD format (configurable via DEFAULT_END_DATE, default: "2025-05-28")
- `liveOnly`: Whether to fetch live programs only (configurable via DEFAULT_LIVE_ONLY, default: false)

## Neo4j Queries

### Example Queries

**Get all channels:**
```cypher
MATCH (c:Channel) RETURN c.name, c.channel_number ORDER BY c.channel_number
```

**Find programs by sport:**
```cypher
MATCH (p:Program)-[:BELONGS_TO]->(s:Sport {name: "Football"})
RETURN p.title, p.start_time, p.end_time
ORDER BY p.start_time
```

**Get channel schedule for a specific date:**
```cypher
MATCH (c:Channel {name: "SuperSport School HD"})-[:BROADCASTS]->(p:Program)
WHERE date(p.start_time) = date("2025-05-22")
RETURN p.title, p.start_time, p.end_time, p.sport
ORDER BY p.start_time
```

**Find live programs:**
```cypher
MATCH (p:Program {is_live: true})
RETURN p.title, p.sport, p.start_time
ORDER BY p.start_time
```

**Programs by genre:**
```cypher
MATCH (p:Program)-[:HAS_GENRE]->(g:Genre)
RETURN g.name as genre, count(p) as program_count
ORDER BY program_count DESC
```

## Error Handling

The application includes comprehensive error handling:

- **API Failures**: Automatic retry with exponential backoff
- **Network Issues**: Configurable timeout and retry settings
- **Data Validation**: Pydantic models ensure data integrity
- **Database Errors**: Graceful handling of Neo4j connection issues
- **Configuration Errors**: Validation of required settings

## Logging

Logs are written to both console and `epg_ingestion.log` file with configurable levels:

- `DEBUG`: Detailed debugging information
- `INFO`: General information about progress
- `WARNING`: Warning messages for non-critical issues
- `ERROR`: Error messages for failures

## Performance Considerations

- **Single Request Processing**: Fetches entire date range in one API call for optimal performance
- **Connection Pooling**: Reuses database connections
- **Indexing**: Creates appropriate indexes for query performance
- **Memory Management**: Efficiently processes large datasets

## Troubleshooting

**Common Issues:**

1. **Neo4j Connection Failed:**
   - Check if Neo4j is running
   - Verify connection details in `.env`
   - Ensure database exists

2. **API Connection Failed:**
   - Check internet connectivity
   - Verify API URL is accessible
   - Check for rate limiting

3. **Import Errors:**
   - Ensure all dependencies are installed
   - Check Python version compatibility

4. **Data Validation Errors:**
   - Check API response format
   - Verify date formats in requests

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.