from pydantic import BaseModel, Field
from typing import List, Optional
from datetime import datetime


class EPGRequest(BaseModel):
    """Request model for EPG API call"""
    country_code: str = Field(default="za", description="Country code")
    channel_only: bool = Field(default=False, description="Channel only flag")
    start_date_time: str = Field(..., description="Start date time in YYYY-MM-DD format")
    end_date_time: str = Field(..., description="End date time in YYYY-MM-DD format")
    live_only: bool = Field(default=False, description="Live only flag")

    def to_query_params(self) -> dict:
        """Convert to query parameters for API call"""
        return {
            "countryCode": self.country_code,
            "channelOnly": str(self.channel_only).lower(),
            "startDateTime": self.start_date_time,
            "endDateTime": self.end_date_time,
            "liveOnly": str(self.live_only).lower()
        }


class Channel(BaseModel):
    """Channel information model"""
    stream: str = Field(..., description="Stream identifier")
    channel_code: str = Field(..., alias="channelCode", description="Channel code")
    name: str = Field(..., description="Channel name")
    icon: str = Field(default="", description="Channel icon URL")
    id: str = Field(..., description="Channel ID")
    mobile_icon: str = Field(default="", alias="mobileIcon", description="Mobile icon URL")
    live_icon: str = Field(default="", alias="liveIcon", description="Live icon URL")
    square_icon: str = Field(default="", alias="squareIcon", description="Square icon URL")
    channel_number: int = Field(..., alias="channelNumber", description="Channel number")


class EPGProgram(BaseModel):
    """EPG Program/Event model"""
    sport: str = Field(..., description="Sport category")
    end: str = Field(..., description="End time")
    is_live: bool = Field(..., alias="isLive", description="Is live flag")
    start: str = Field(..., description="Start time")
    title: str = Field(..., description="Program title")
    name: str = Field(..., description="Program name")
    rating: str = Field(..., description="Content rating")
    synopsis: str = Field(..., description="Program synopsis")
    thumbnail_uri: Optional[str] = Field(None, alias="thumbnailUri", description="Thumbnail URL")
    channel: List[Channel] = Field(..., description="Channel information")
    packages: List[str] = Field(..., description="Available packages")
    showmax: bool = Field(default=False, description="Available on Showmax")
    sub_genres: List[str] = Field(..., alias="subGenres", description="Sub-genres")

    @property
    def start_datetime(self) -> datetime:
        """Parse start time to datetime object"""
        return datetime.strptime(self.start, "%m/%d/%Y %H:%M:%S")

    @property
    def end_datetime(self) -> datetime:
        """Parse end time to datetime object"""
        return datetime.strptime(self.end, "%m/%d/%Y %H:%M:%S")

    @property
    def duration_minutes(self) -> int:
        """Calculate duration in minutes"""
        return int((self.end_datetime - self.start_datetime).total_seconds() / 60)


class EPGResponse(BaseModel):
    """Complete EPG API response model"""
    programs: List[EPGProgram]

    @classmethod
    def from_api_response(cls, data: List[dict]) -> "EPGResponse":
        """Create EPGResponse from raw API data"""
        programs = [EPGProgram(**program) for program in data]
        return cls(programs=programs)


# Neo4j Node Models
class ChannelNode(BaseModel):
    """Neo4j Channel node model"""
    id: str
    name: str
    channel_code: str
    channel_number: int
    stream: str
    icon: str = ""
    mobile_icon: str = ""
    live_icon: str = ""
    square_icon: str = ""


class ProgramNode(BaseModel):
    """Neo4j Program node model"""
    title: str
    name: str
    sport: str
    rating: str
    synopsis: str
    start_time: datetime
    end_time: datetime
    duration_minutes: int
    is_live: bool
    showmax: bool
    thumbnail_uri: str = ""


class SportNode(BaseModel):
    """Neo4j Sport node model"""
    name: str


class GenreNode(BaseModel):
    """Neo4j Genre node model"""
    name: str


class PackageNode(BaseModel):
    """Neo4j Package node model"""
    name: str


class TeamNode(BaseModel):
    """Neo4j Team/School node model"""
    name: str
    type: str = "team"


class TournamentNode(BaseModel):
    """Neo4j Tournament/Competition node model"""
    name: str
    type: str = "tournament"


class VenueNode(BaseModel):
    """Neo4j Venue node model"""
    name: str
    location: Optional[str] = None


class LocationNode(BaseModel):
    """Neo4j Location node model"""
    name: str
    type: str = "location"


class PersonNode(BaseModel):
    """Neo4j Person node model"""
    name: str
    type: str = "person"


class AgeGroupNode(BaseModel):
    """Neo4j Age Group node model"""
    name: str
    type: str = "age_group"


class EpisodeNode(BaseModel):
    """Neo4j Episode node model"""
    info: str
    season: Optional[str] = None
    episode: Optional[str] = None
    total_episodes: Optional[str] = None


# Relationship Models
class BroadcastsRelationship(BaseModel):
    """BROADCASTS relationship between Channel and Program"""
    start_time: datetime
    end_time: datetime


class BelongsToRelationship(BaseModel):
    """BELONGS_TO relationship for categorization"""
    pass


class HasGenreRelationship(BaseModel):
    """HAS_GENRE relationship between Program and Genre"""
    pass


class AvailableOnRelationship(BaseModel):
    """AVAILABLE_ON relationship between Program and Package"""
    pass


class CompetesInRelationship(BaseModel):
    """COMPETES_IN relationship between Team and Tournament"""
    pass


class PlaysAtRelationship(BaseModel):
    """PLAYS_AT relationship between Program and Venue"""
    pass


class LocatedInRelationship(BaseModel):
    """LOCATED_IN relationship between Venue and Location"""
    pass


class FeaturesRelationship(BaseModel):
    """FEATURES relationship between Program and Person"""
    role: Optional[str] = None


class HasAgeGroupRelationship(BaseModel):
    """HAS_AGE_GROUP relationship between Program and AgeGroup"""
    pass


class PartOfSeriesRelationship(BaseModel):
    """PART_OF_SERIES relationship between Program and Episode"""
    pass


class CompetesAgainstRelationship(BaseModel):
    """COMPETES_AGAINST relationship between Teams"""
    program_id: str
    date: datetime
