#!/usr/bin/env python3
"""
Test script to verify channel field mapping without Neo4j connection
"""

from models import EPGProgram
import json

def test_channel_field_mapping():
    """Test that channel fields are properly mapped when using model_dump(by_alias=True)"""
    
    # Sample program data with channel (as it would come from the API)
    program_data = {
        "title": "Test Football Match",
        "name": "Test Football Match",
        "sport": "Football",
        "rating": "PG",
        "synopsis": "A test football match",
        "start": "12/25/2024 14:00:00",
        "end": "12/25/2024 16:00:00",
        "isLive": True,
        "showmax": False,
        "thumbnailUri": "https://example.com/thumb.jpg",
        "channel": [
            {
                "id": "supersport1",
                "name": "SuperSport 1",
                "channelCode": "SS1",
                "channelNumber": 201,
                "stream": "supersport1_stream",
                "icon": "https://example.com/ss1_icon.png",
                "mobileIcon": "https://example.com/ss1_mobile.png",
                "liveIcon": "https://example.com/ss1_live.png",
                "squareIcon": "https://example.com/ss1_square.png"
            }
        ],
        "packages": ["DStv Premium", "Sports Package"],
        "subGenres": ["Live Sports", "Football"]
    }
    
    print("🧪 Testing Channel Field Mapping")
    print("=" * 50)
    
    print("1. Original program data (as from API):")
    print(json.dumps(program_data, indent=2))
    
    # Create EPGProgram object
    try:
        program = EPGProgram(**program_data)
        print("\n2. ✅ EPGProgram object created successfully")
        
        # Test channel access
        channel = program.channel[0]
        print(f"\n3. Channel object properties:")
        print(f"   ID: {channel.id}")
        print(f"   Name: {channel.name}")
        print(f"   Channel Code: {channel.channel_code}")
        print(f"   Channel Number: {channel.channel_number}")
        print(f"   Stream: {channel.stream}")
        print(f"   Mobile Icon: {channel.mobile_icon}")
        print(f"   Live Icon: {channel.live_icon}")
        print(f"   Square Icon: {channel.square_icon}")
        
        # Test model_dump() without by_alias
        dumped_normal = program.model_dump()
        print(f"\n4. model_dump() output (snake_case):")
        channel_data_normal = dumped_normal['channel'][0]
        print(f"   channelCode -> channel_code: {channel_data_normal.get('channel_code', 'MISSING')}")
        print(f"   channelNumber -> channel_number: {channel_data_normal.get('channel_number', 'MISSING')}")
        print(f"   mobileIcon -> mobile_icon: {channel_data_normal.get('mobile_icon', 'MISSING')}")
        
        # Test model_dump(by_alias=True)
        dumped_alias = program.model_dump(by_alias=True)
        print(f"\n5. model_dump(by_alias=True) output (camelCase):")
        channel_data_alias = dumped_alias['channel'][0]
        print(f"   channelCode: {channel_data_alias.get('channelCode', 'MISSING')}")
        print(f"   channelNumber: {channel_data_alias.get('channelNumber', 'MISSING')}")
        print(f"   mobileIcon: {channel_data_alias.get('mobileIcon', 'MISSING')}")
        print(f"   liveIcon: {channel_data_alias.get('liveIcon', 'MISSING')}")
        print(f"   squareIcon: {channel_data_alias.get('squareIcon', 'MISSING')}")
        
        # Verify the fix
        print(f"\n6. ✅ Verification:")
        if (channel_data_alias.get('channelCode') and 
            channel_data_alias.get('channelNumber') and
            channel_data_alias.get('mobileIcon')):
            print("   ✅ All channel fields are properly preserved with by_alias=True")
            print("   ✅ Neo4j client will now receive correct field names")
            return True
        else:
            print("   ❌ Some channel fields are missing")
            return False
            
    except Exception as e:
        print(f"❌ Error creating EPGProgram: {e}")
        return False

def simulate_neo4j_channel_creation():
    """Simulate what the Neo4j client would receive"""
    
    print(f"\n7. 🔧 Simulating Neo4j Channel Creation:")
    
    # This is what the Neo4j client would receive after model_dump(by_alias=True)
    channel_data = {
        "id": "supersport1",
        "name": "SuperSport 1",
        "channelCode": "SS1",
        "channelNumber": 201,
        "stream": "supersport1_stream",
        "icon": "https://example.com/ss1_icon.png",
        "mobileIcon": "https://example.com/ss1_mobile.png",
        "liveIcon": "https://example.com/ss1_live.png",
        "squareIcon": "https://example.com/ss1_square.png"
    }
    
    # Simulate the Neo4j query parameters
    neo4j_params = {
        "channel_id": channel_data.get("id", ""),
        "name": channel_data.get("name", ""),
        "channel_code": channel_data.get("channelCode", ""),
        "channel_number": channel_data.get("channelNumber", 0),
        "stream": channel_data.get("stream", ""),
        "icon": channel_data.get("icon", ""),
        "mobile_icon": channel_data.get("mobileIcon", ""),
        "live_icon": channel_data.get("liveIcon", ""),
        "square_icon": channel_data.get("squareIcon", "")
    }
    
    print("   Neo4j query parameters:")
    for key, value in neo4j_params.items():
        status = "✅" if value else "❌"
        print(f"   {status} {key}: {value}")
    
    # Check if all important fields have values
    important_fields = ["channel_id", "name", "channel_code", "channel_number"]
    all_good = all(neo4j_params[field] for field in important_fields)
    
    if all_good:
        print("\n   ✅ All important channel fields have values!")
        print("   ✅ Channel nodes will be created properly in Neo4j")
        return True
    else:
        print("\n   ❌ Some important channel fields are missing values")
        return False

if __name__ == "__main__":
    success1 = test_channel_field_mapping()
    success2 = simulate_neo4j_channel_creation()
    
    if success1 and success2:
        print(f"\n🎉 Channel field mapping test PASSED!")
        print("✅ The fix for channel_number=0 and channel_code='' is working")
        print("✅ Using model_dump(by_alias=True) preserves original API field names")
        print("✅ Neo4j client will receive proper channel data")
    else:
        print(f"\n❌ Channel field mapping test FAILED!")
