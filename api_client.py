import requests
from typing import Optional, Dict, Any
import logging
from models import EPGRequest, EPGResponse
import time
from config import config


class SuperSportAPIClient:
    """Client for SuperSport EPG API"""

    def __init__(self, base_url: str = "https://supersport.com/apix/guide/v5.3"):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'application/json',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        self.logger = logging.getLogger(__name__)

    def fetch_epg_data(self, request: EPGRequest, retry_count: int = 3, delay: float = 1.0) -> Optional[EPGResponse]:
        """
        Fetch EPG data from SuperSport API

        Args:
            request: EPG request parameters
            retry_count: Number of retry attempts
            delay: Delay between retries in seconds

        Returns:
            EPGResponse object or None if failed
        """
        url = f"{self.base_url}/tvguide"
        params = request.to_query_params()

        self.logger.info(f"Fetching EPG data from {url} with params: {params}")

        for attempt in range(retry_count):
            try:
                response = self.session.get(url, params=params, timeout=30)
                response.raise_for_status()

                data = response.json()
                self.logger.info(f"Successfully fetched {len(data)} programs")

                return EPGResponse.from_api_response(data)

            except requests.exceptions.RequestException as e:
                self.logger.error(f"Attempt {attempt + 1} failed: {e}")
                if attempt < retry_count - 1:
                    self.logger.info(f"Retrying in {delay} seconds...")
                    time.sleep(delay)
                    delay *= 2  # Exponential backoff
                else:
                    self.logger.error("All retry attempts failed")
                    return None

            except Exception as e:
                self.logger.error(f"Unexpected error: {e}")
                return None

    def fetch_epg_data_raw(self, request: EPGRequest) -> Optional[Dict[str, Any]]:
        """
        Fetch raw EPG data without parsing

        Args:
            request: EPG request parameters

        Returns:
            Raw JSON response or None if failed
        """
        url = f"{self.base_url}/tvguide"
        params = request.to_query_params()

        try:
            response = self.session.get(url, params=params, timeout=30)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            self.logger.error(f"Failed to fetch raw data: {e}")
            return None

    def test_connection(self) -> bool:
        """
        Test API connection

        Returns:
            True if connection successful, False otherwise
        """
        try:
            # Test with minimal request using config defaults
            test_request = create_epg_request("2025-01-20", "2025-01-21")
            response = self.fetch_epg_data_raw(test_request)
            return response is not None
        except Exception as e:
            self.logger.error(f"Connection test failed: {e}")
            return False


# Utility functions
def create_epg_request(start_date_time: str = None, end_date_time: str = None,
                      country_code: str = None, channel_only: bool = None,
                      live_only: bool = None) -> EPGRequest:
    """
    Create EPGRequest with configuration defaults

    Args:
        start_date_time: Start date in YYYY-MM-DD format (uses config default if None)
        end_date_time: End date in YYYY-MM-DD format (uses config default if None)
        country_code: Country code (uses config default if None)
        channel_only: Channel only flag (uses config default if None)
        live_only: Live only flag (uses config default if None)

    Returns:
        EPGRequest object with appropriate defaults
    """
    return EPGRequest(
        start_date_time=start_date_time or config.api.default_start_date,
        end_date_time=end_date_time or config.api.default_end_date,
        country_code=country_code or config.api.default_country_code,
        channel_only=channel_only if channel_only is not None else config.api.default_channel_only,
        live_only=live_only if live_only is not None else config.api.default_live_only
    )


def create_date_range_requests(start_date: str, end_date: str, days_per_request: int = None) -> list[EPGRequest]:
    """
    Create EPG request for the specified date range without splitting

    Args:
        start_date: Start date in YYYY-MM-DD format
        end_date: End date in YYYY-MM-DD format
        days_per_request: Deprecated parameter, ignored for backward compatibility

    Returns:
        List containing a single EPGRequest object for the entire date range
    """
    # Create a single request for the entire date range using config defaults
    request = create_epg_request(start_date, end_date)
    return [request]



